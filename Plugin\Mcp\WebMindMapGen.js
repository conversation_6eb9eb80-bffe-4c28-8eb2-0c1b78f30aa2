// Plugin/Mcp/WebMindMapGen.js - 网页思维导图生成MCP插件
const BaseMcpPlugin = require('./BaseMcpPlugin');

class WebMindMapGenMcp extends BaseMcpPlugin {
    constructor() {
        super();
        this.name = 'WebMindMapGen';
        this.description = '解析网页链接内容并生成思维导图。智能提取网页核心信息，生成结构化的思维导图可视化，支持多种样式主题。适用于网页内容分析、知识整理、信息可视化等场景';
        this.vcpName = 'WebMindMapGen';
    }

    getParameters() {
        return {
            type: 'object',
            properties: {
                url: {
                    type: 'string',
                    description: '要解析的网页URL地址，支持http和https协议',
                    minLength: 1,
                    maxLength: 2000
                },
                width: {
                    type: 'number',
                    description: '输出图片宽度（像素）',
                    minimum: 2400,
                    maximum: 4000,
                    default: 3000
                },
                height: {
                    type: 'number',
                    description: '输出图片高度（像素）',
                    minimum: 1600,
                    maximum: 3000,
                    default: 2000
                },
                waitTime: {
                    type: 'number',
                    description: '渲染等待时间（毫秒），用于确保图片完全渲染',
                    minimum: 8000,
                    maximum: 30000,
                    default: 12000
                },
                style: {
                    type: 'string',
                    description: '思维导图样式主题，AI会根据网页内容自动选择最合适的风格',
                    enum: [
                        'default',      // 经典白色背景，适合正式文档
                        'colorful',     // 彩色渐变背景，视觉丰富
                        'dark',         // 深色主题，适合技术内容
                        'minimal',      // 简约风格，突出内容
                        'web',          // 网页风格，蓝色主题（推荐）
                        'tech'          // 技术风格，绿色代码主题
                    ],
                    default: 'web'
                }
            },
            required: ['url']
        };
    }

    async execute(args) {
        // 验证参数
        this.validateArgs(args);

        // 设置默认值
        if (!args.width) args.width = 3000;
        if (!args.height) args.height = 2000;
        if (!args.waitTime) args.waitTime = 12000;
        if (!args.style) args.style = 'web';

        // AI根据URL类型自动选择样式
        if (args.style === 'web') {
            args.style = this.selectStyleByUrl(args.url);
            this.log('info', `AI自动选择样式: ${args.style}`);
        }

        this.log('info', `开始生成网页思维导图`, {
            url: args.url,
            width: args.width,
            height: args.height,
            style: args.style
        });

        try {
            // 调用对应的VCP插件
            const result = await this.callVcpPlugin(args);

            // VCP插件现在返回JSON格式
            let parsedResult;
            try {
                parsedResult = typeof result === 'string' ? JSON.parse(result) : result;
            } catch (e) {
                // 如果解析失败，可能是旧格式的字符串
                this.log('warning', `VCP插件返回格式异常，尝试兼容处理: ${e.message}`);

                if (typeof result === 'string') {
                    if (result.includes('失败') || result.includes('错误')) {
                        return {
                            type: 'web_mindmap_generation',
                            status: 'error',
                            message: result,
                            data: {
                                url: args.url,
                                error_details: result,
                                timestamp: new Date().toISOString()
                            }
                        };
                    }

                    return {
                        type: 'web_mindmap_generation',
                        status: 'success',
                        message: '网页思维导图生成完成',
                        data: {
                            url: args.url,
                            width: args.width,
                            height: args.height,
                            style: args.style,
                            markdown_display: result,
                            generation_info: {
                                model: 'gpt-4o-mini',
                                timestamp: new Date().toISOString(),
                                plugin_version: '1.0.0'
                            }
                        }
                    };
                }
            }

            // 处理标准JSON格式返回
            if (parsedResult.status === 'error') {
                this.log('error', `网页思维导图生成失败: ${parsedResult.message}`);

                return {
                    type: 'web_mindmap_generation',
                    status: 'error',
                    message: parsedResult.message,
                    data: {
                        url: args.url,
                        error_type: parsedResult.data?.error_type || 'UnknownError',
                        error_details: parsedResult.data?.error_details || parsedResult.message,
                        timestamp: parsedResult.data?.timestamp || new Date().toISOString()
                    }
                };
            }

            // 成功情况
            this.log('success', `网页思维导图生成成功`);

            return {
                type: 'web_mindmap_generation',
                status: 'success',
                message: parsedResult.message || '网页思维导图生成完成',
                data: {
                    url: args.url,
                    web_title: parsedResult.data?.web_title,
                    web_content_length: parsedResult.data?.web_content_length,
                    width: args.width,
                    height: args.height,
                    style: args.style,
                    markdown_content: parsedResult.data?.markdown_content,
                    image_path: parsedResult.data?.image_path,
                    image_url: parsedResult.data?.image_url,
                    markdown_display: parsedResult.data?.markdown_display,
                    generation_info: {
                        model: parsedResult.data?.generation_info?.model || 'gpt-4o-mini',
                        timestamp: parsedResult.data?.generation_info?.timestamp || new Date().toISOString(),
                        plugin_version: parsedResult.data?.generation_info?.plugin_version || '1.0.0'
                    }
                }
            };

        } catch (error) {
            this.log('error', `网页思维导图生成失败: ${error.message}`);
            
            return {
                type: 'web_mindmap_generation',
                status: 'error',
                message: `网页思维导图生成失败: ${error.message}`,
                data: {
                    url: args.url,
                    error_type: error.name || 'UnknownError',
                    error_details: error.message,
                    timestamp: new Date().toISOString()
                }
            };
        }
    }

    // 重写初始化方法
    async initialize() {
        await super.initialize();
        
        // 检查VCP插件是否可用
        try {
            const pluginManager = global.pluginManager;
            if (!pluginManager.getPlugin(this.vcpName)) {
                throw new Error(`找不到对应的VCP插件: ${this.vcpName}`);
            }
            
            // 检查必要的环境变量
            const requiredEnvVars = ['OPENAI_API_KEY'];
            for (const envVar of requiredEnvVars) {
                if (!process.env[envVar]) {
                    this.log('warning', `未配置${envVar}环境变量，可能影响AI生成功能`);
                }
            }
        } catch (error) {
            this.log('error', `插件初始化失败: ${error.message}`);
            throw error;
        }
        
        return true;
    }

    /**
     * AI根据URL类型自动选择合适的样式
     * @param {string} url - 网页URL
     * @returns {string} - 选择的样式名称
     */
    selectStyleByUrl(url) {
        const urlLower = url.toLowerCase();

        // 定义URL模式和对应的样式
        const styleRules = [
            {
                patterns: ['github.com', 'gitlab.com', 'bitbucket.org', 'stackoverflow.com'],
                style: 'tech',
                description: '技术风格'
            },
            {
                patterns: ['wikipedia.org', 'baike.baidu.com', 'zhihu.com'],
                style: 'minimal',
                description: '简约风格'
            },
            {
                patterns: ['news', 'blog', 'medium.com', 'csdn.net'],
                style: 'colorful',
                description: '彩色风格'
            },
            {
                patterns: ['docs', 'documentation', 'manual', 'guide'],
                style: 'default',
                description: '经典风格'
            },
            {
                patterns: ['dark', 'black', 'night'],
                style: 'dark',
                description: '深色主题'
            }
        ];

        // 检查URL匹配的模式
        for (const rule of styleRules) {
            for (const pattern of rule.patterns) {
                if (urlLower.includes(pattern)) {
                    this.log('debug', `匹配URL模式"${pattern}"，选择${rule.description}`);
                    return rule.style;
                }
            }
        }

        // 默认使用web风格
        return 'web';
    }
}

module.exports = WebMindMapGenMcp;
