# 网页解析工具插件配置文件示例
# 复制此文件为 config.env 并根据需要修改配置

# 网页解析配置
WEB_PARSER_TIMEOUT=30000
WEB_PARSER_MAX_CONTENT_LENGTH=50000
WEB_PARSER_MAX_TOKENS=8000
WEB_PARSER_USER_AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36 WebParserTool/1.0.0

# Token限制配置
WEB_PARSER_ENABLE_TOKEN_LIMIT=true
WEB_PARSER_TOKEN_TRUNCATE_ENABLED=true
WEB_PARSER_TOKEN_MAX_TOKENS=8000
WEB_PARSER_TOKEN_TRUNCATE_MARKER=\n\n[内容已截断，超过最大token限制]

# 内容质量配置
WEB_PARSER_CONTENT_QUALITY=high

# 代理配置（可选）
WEB_PARSER_USE_PROXY=false
WEB_PARSER_PROXY_SERVER=

# 调试模式
DebugMode=false
