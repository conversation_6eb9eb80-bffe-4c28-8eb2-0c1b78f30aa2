// 测试智能解析功能
const { spawn } = require('child_process');

async function testSmartParsing() {
    console.log('🧪 测试智能解析功能...\n');

    const testCases = [
        {
            name: '测试微信公众号文章',
            input: {
                url: 'https://mp.weixin.qq.com/s/Txg56j22U5ixaw6nSs9MHQ'
            }
        },
        {
            name: '测试百度首页',
            input: {
                url: 'https://www.baidu.com'
            }
        }
    ];

    for (let i = 0; i < testCases.length; i++) {
        const testCase = testCases[i];
        console.log(`📋 ${i + 1}. ${testCase.name}`);
        console.log(`   URL: ${testCase.input.url}`);

        try {
            const result = await runPlugin(testCase.input);
            
            if (result.status === 'success') {
                console.log(`   ✅ 成功: ${result.message}`);
                console.log(`   📄 标题: ${result.data.title}`);
                console.log(`   📝 内容长度: ${result.data.content_length} 字符`);
                console.log(`   🔧 获取方法: ${result.data.method}`);
                console.log(`   🎯 提取方法: ${result.data.metadata.extractionMethod}`);
                console.log(`   📊 质量分数: ${result.data.metadata.contentScore || 0}`);
                
                if (result.data.token_info) {
                    console.log(`   🎫 Token信息: ${result.data.token_info.finalTokens}/${result.data.token_info.maxTokens}`);
                }
            } else {
                console.log(`   ❌ 失败: ${result.message}`);
            }
        } catch (error) {
            console.log(`   ❌ 错误: ${error.message}`);
        }

        if (i < testCases.length - 1) {
            console.log('\n⏳ 等待3秒后继续下一个测试...\n');
            await new Promise(resolve => setTimeout(resolve, 3000));
        }
    }

    console.log('\n🎉 测试完成！');
}

function runPlugin(input) {
    return new Promise((resolve, reject) => {
        const child = spawn('node', ['WebParserTool.js'], {
            stdio: ['pipe', 'pipe', 'pipe']
        });

        let output = '';
        let error = '';

        child.stdout.on('data', (data) => {
            output += data.toString();
        });

        child.stderr.on('data', (data) => {
            error += data.toString();
        });

        child.on('close', (code) => {
            if (code === 0 && output) {
                try {
                    const result = JSON.parse(output);
                    resolve(result);
                } catch (parseError) {
                    reject(new Error(`JSON解析失败: ${parseError.message}`));
                }
            } else {
                reject(new Error(`插件执行失败，退出码: ${code}, 错误: ${error}`));
            }
        });

        child.stdin.write(JSON.stringify(input));
        child.stdin.end();
    });
}

testSmartParsing().catch(console.error);
