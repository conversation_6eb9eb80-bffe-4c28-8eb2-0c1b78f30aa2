#!/usr/bin/env node
const { tavily } = require('@tavily/core'); // Using the official Node.js client
const stdin = require('process').stdin;
const { tokenUtils } = require('../../utils/tokenUtils');

// 引入VCP日志系统
let logger;
try {
    const loggerPath = process.env.NODE_ENV === 'production' 
        ? '../../utils/logger.cjs' 
        : '../../utils/logger.js';
    const vcpLogger = require(loggerPath).default || require(loggerPath);
    
    // 为JSON输出插件创建特殊的logger，强制输出到stderr
    logger = {
        info: (component, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [i] [${component}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        },
        error: (component, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [x] [${component}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        },
        warning: (component, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [!] [${component}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        },
        debug: (component, msg, data) => {
            if (process.env.DebugMode === 'true') {
                const output = `[${new Date().toLocaleString('zh-CN')}] [*] [${component}] ${msg}`;
                console.error(output);
                if (data) console.error(data);
            }
        },
        plugin: (name, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [P] [插件-${name}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        }
    };
} catch (e) {
    // 回退到传统日志，也输出到stderr
    logger = {
        info: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        error: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        warning: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        debug: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        plugin: (name, msg, data) => console.error(`[插件-${name}] ${msg}`, data || '')
    }
}

async function main() {
    let inputData = '';
    stdin.setEncoding('utf8');

    stdin.on('data', function(chunk) {
        inputData += chunk;
    });

    stdin.on('end', async function() {
        let output = {};

        try {
            if (!inputData.trim()) {
                throw new Error("No input data received from stdin.");
            }

            const data = JSON.parse(inputData);

            const query = data.query;
            let topic = data.topic || 'general'; // Default to 'general'
            let searchDepth = data.search_depth || 'basic'; // Default to 'basic'
            let maxResults = data.max_results || 10; // Default to 10

            if (!query) {
                throw new Error("Missing required argument: query");
            }

            // Validate topic
            const validTopics = ['general', 'news', 'finance', 'research', 'code'];
            if (!validTopics.includes(topic)) {
                topic = 'general';
            }

            // Validate search_depth
            const validDepths = ['basic', 'advanced'];
            if (!validDepths.includes(searchDepth)) {
                searchDepth = 'basic';
            }

            logger.info('TavilySearch', `开始搜索: "${query}", 主题: ${topic}, 深度: ${searchDepth}, 最大结果: ${maxResults}`);

            // Validate max_results
            try {
                maxResults = parseInt(maxResults, 10);
                if (isNaN(maxResults) || maxResults < 5 || maxResults > 100) {
                    maxResults = 10; // Default to 10 if invalid or out of range
                }
            } catch (e) {
                maxResults = 10; // Default if parsing fails
            }

            const apiKey = process.env.TavilyKey; // Use the correct environment variable name
            if (!apiKey) {
                throw new Error("TavilyKey environment variable not set.");
            }

            const tvly = tavily({ apiKey });

            const response = await tvly.search(query, {
                search_depth: searchDepth,
                topic: topic,
                max_results: maxResults,
                include_answer: false, // Usually just want results for AI processing
                include_raw_content: false,
                include_images: false
            });

            logger.info('TavilySearch', `搜索完成，获得 ${response.results?.length || 0} 个结果`);

            // Tavily Node client returns a JSON-serializable object
            // Convert to string for token processing
            const responseString = JSON.stringify(response, null, 2);
            
            logger.info('TavilySearch', `原始搜索结果长度: ${responseString.length} 字符`);
            
            // 使用token工具处理内容截断
            const tokenResult = tokenUtils.processPluginContent(responseString, 'TAVILYSEARCH');
            
            logger.info('TavilySearch', `Token处理结果: 原始${tokenResult.originalTokens}个token, 最终${tokenResult.finalTokens}个token, 是否截断: ${tokenResult.truncated}`);
            
            if (tokenResult.truncated) {
                logger.warning('TavilySearch', `搜索结果已截断，原始token数量超过限制 (${tokenResult.originalTokens} > ${tokenResult.config.maxTokens})`);
            }

            // Ensure the result is a string for output
            output = { 
                status: "success", 
                result: tokenResult.content,
                token_info: {
                    original_tokens: tokenResult.originalTokens,
                    final_tokens: tokenResult.finalTokens,
                    truncated: tokenResult.truncated,
                    max_tokens: tokenResult.config.maxTokens,
                    truncate_enabled: tokenResult.config.enabled,
                    original_results_count: response.results?.length || 0
                }
            };

        } catch (e) {
            let errorMessage;
            if (e instanceof SyntaxError) {
                errorMessage = "无效的 JSON input.";
            } else if (e instanceof Error) {
                errorMessage = e.message;
            } else {
                errorMessage = "An unknown error occurred.";
            }
            
            logger.error('TavilySearch', `搜索失败: ${errorMessage}`);
            output = { status: "error", error: `Tavily Search Error: ${errorMessage}` };
        }

        // Output JSON to stdout
        process.stdout.write(JSON.stringify(output, null, 2));
    });
}

main().catch(error => {
    // Catch any unhandled promise rejections from main
    logger.error('TavilySearch', `未处理的错误: ${error.message || error}`);
    process.stdout.write(JSON.stringify({ status: "error", error: `Unhandled Plugin Error: ${error.message || error}` }));
    process.exit(1); // Indicate failure
});
