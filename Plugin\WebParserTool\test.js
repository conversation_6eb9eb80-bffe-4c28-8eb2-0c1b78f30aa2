#!/usr/bin/env node
// Plugin/WebParserTool/test.js - 网页解析工具测试脚本

const { spawn } = require('child_process');
const path = require('path');

/**
 * 测试网页解析工具
 */
async function testWebParserTool() {
    console.log('🧪 开始测试网页解析工具...\n');

    // 测试用例
    const testCases = [
        {
            name: '测试Wikipedia页面',
            input: {
                url: 'https://zh.wikipedia.org/wiki/人工智能'
            }
        },
        {
            name: '测试新闻网站',
            input: {
                url: 'https://www.bbc.com/zhongwen'
            }
        },
        {
            name: '测试技术文档',
            input: {
                url: 'https://nodejs.org/en/docs/'
            }
        }
    ];

    for (let i = 0; i < testCases.length; i++) {
        const testCase = testCases[i];
        console.log(`📋 ${i + 1}. ${testCase.name}`);
        console.log(`   URL: ${testCase.input.url}`);
        
        try {
            const result = await runPlugin(testCase.input);
            
            if (result.status === 'success') {
                console.log(`   ✅ 成功: ${result.message}`);
                if (result.data.title) {
                    console.log(`   📄 标题: ${result.data.title}`);
                }
                if (result.data.content_length) {
                    console.log(`   📊 内容长度: ${result.data.content_length} 字符`);
                }
                if (result.data.method) {
                    console.log(`   🔧 获取方法: ${result.data.method}`);
                }
            } else {
                console.log(`   ❌ 失败: ${result.message}`);
                console.log(`   🔍 错误详情: ${result.data.error_details}`);
            }
        } catch (error) {
            console.log(`   💥 异常: ${error.message}`);
        }
        
        console.log('');
        
        // 避免请求过于频繁
        if (i < testCases.length - 1) {
            console.log('⏳ 等待3秒后继续下一个测试...\n');
            await new Promise(resolve => setTimeout(resolve, 3000));
        }
    }

    console.log('🎉 测试完成！');
}

/**
 * 运行插件
 * @param {Object} input - 输入参数
 * @returns {Promise<Object>} - 插件输出结果
 */
function runPlugin(input) {
    return new Promise((resolve, reject) => {
        const pluginPath = path.join(__dirname, 'WebParserTool.js');
        const child = spawn('node', [pluginPath], {
            stdio: ['pipe', 'pipe', 'pipe']
        });

        let output = '';
        let errorOutput = '';

        // 发送输入数据
        child.stdin.write(JSON.stringify(input));
        child.stdin.end();

        child.stdout.on('data', (data) => {
            output += data.toString();
        });

        child.stderr.on('data', (data) => {
            errorOutput += data.toString();
        });

        child.on('close', (code) => {
            if (code === 0) {
                try {
                    const result = JSON.parse(output);
                    resolve(result);
                } catch (e) {
                    reject(new Error(`解析输出JSON失败: ${e.message}\n输出内容: ${output}`));
                }
            } else {
                reject(new Error(`插件进程退出码: ${code}\n错误输出: ${errorOutput}\n标准输出: ${output}`));
            }
        });

        child.on('error', (error) => {
            reject(new Error(`启动插件失败: ${error.message}`));
        });
    });
}

// 运行测试
if (require.main === module) {
    testWebParserTool().catch(error => {
        console.error('❌ 测试失败:', error.message);
        process.exit(1);
    });
}
