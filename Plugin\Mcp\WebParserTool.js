// Plugin/Mcp/WebParserTool.js - 网页解析工具MCP插件
const BaseMcpPlugin = require('./BaseMcpPlugin');

class WebParserToolMcp extends BaseMcpPlugin {
    constructor() {
        super();
        this.name = 'WebParserTool';
        this.description = '智能网页内容解析工具。能够解析网页链接，提取核心文本内容，支持JavaScript渲染页面，自动清理广告和无关内容。适用于网页内容分析、信息提取、内容整理等场景';
        this.vcpName = 'WebParserTool';
    }

    getParameters() {
        return {
            type: 'object',
            properties: {
                url: {
                    type: 'string',
                    description: '要解析的网页URL地址，支持http和https协议',
                    minLength: 1,
                    maxLength: 2000
                }
            },
            required: ['url']
        };
    }

    async execute(args) {
        // 验证参数
        this.validateArgs(args);

        this.log('info', `开始解析网页内容`, {
            url: args.url
        });

        try {
            // 调用对应的VCP插件
            const result = await this.callVcpPlugin(args);

            // VCP插件返回JSON格式
            let parsedResult;
            try {
                parsedResult = typeof result === 'string' ? JSON.parse(result) : result;
            } catch (e) {
                // 如果解析失败，可能是旧格式的字符串
                this.log('warning', `VCP插件返回格式异常，尝试兼容处理: ${e.message}`);

                if (typeof result === 'string') {
                    if (result.includes('失败') || result.includes('错误')) {
                        return {
                            type: 'web_content_parsing',
                            status: 'error',
                            message: result,
                            data: {
                                url: args.url,
                                error_details: result,
                                timestamp: new Date().toISOString()
                            }
                        };
                    }

                    return {
                        type: 'web_content_parsing',
                        status: 'success',
                        message: '网页内容解析完成',
                        data: {
                            url: args.url,
                            content: result,
                            timestamp: new Date().toISOString()
                        }
                    };
                }
            }

            // 处理标准JSON格式返回
            if (parsedResult.status === 'error') {
                this.log('error', `网页解析失败: ${parsedResult.message}`);

                return {
                    type: 'web_content_parsing',
                    status: 'error',
                    message: parsedResult.message,
                    data: {
                        url: args.url,
                        error_type: parsedResult.data?.error_type || 'UnknownError',
                        error_details: parsedResult.data?.error_details || parsedResult.message,
                        timestamp: parsedResult.data?.timestamp || new Date().toISOString()
                    }
                };
            }

            // 成功情况
            this.log('success', `网页解析成功`, {
                url: args.url,
                title: parsedResult.data?.title,
                content_length: parsedResult.data?.content_length,
                method: parsedResult.data?.method
            });

            return {
                type: 'web_content_parsing',
                status: 'success',
                message: parsedResult.message || '网页内容解析完成',
                data: {
                    url: parsedResult.data?.url || args.url,
                    title: parsedResult.data?.title,
                    content: parsedResult.data?.content,
                    content_length: parsedResult.data?.content_length,
                    metadata: parsedResult.data?.metadata || {},
                    method: parsedResult.data?.method,
                    timestamp: parsedResult.data?.timestamp || new Date().toISOString(),
                    markdown_display: parsedResult.data?.markdown_display
                }
            };

        } catch (error) {
            this.log('error', `网页解析失败: ${error.message}`);
            
            return {
                type: 'web_content_parsing',
                status: 'error',
                message: `网页解析失败: ${error.message}`,
                data: {
                    url: args.url,
                    error_type: error.name || 'UnknownError',
                    error_details: error.message,
                    timestamp: new Date().toISOString()
                }
            };
        }
    }

    // 重写初始化方法
    async initialize() {
        await super.initialize();
        
        // 检查VCP插件是否可用
        try {
            const pluginManager = global.pluginManager;
            if (!pluginManager.getPlugin(this.vcpName)) {
                throw new Error(`找不到对应的VCP插件: ${this.vcpName}`);
            }
            
            this.log('info', '网页解析工具MCP插件初始化完成');
        } catch (error) {
            this.log('error', `插件初始化失败: ${error.message}`);
            throw error;
        }
        
        return true;
    }

    /**
     * 验证URL格式
     * @param {string} url - URL字符串
     * @returns {boolean} - 是否有效
     */
    isValidUrl(url) {
        try {
            new URL(url);
            return true;
        } catch (e) {
            return false;
        }
    }

    /**
     * 重写参数验证方法，添加URL格式检查
     */
    validateArgs(args) {
        super.validateArgs(args);
        
        if (args.url && !this.isValidUrl(args.url) && !args.url.startsWith('http')) {
            // 尝试添加协议
            args.url = 'https://' + args.url;
            if (!this.isValidUrl(args.url)) {
                throw new Error('无效的URL格式');
            }
        }
    }
}

module.exports = WebParserToolMcp;
