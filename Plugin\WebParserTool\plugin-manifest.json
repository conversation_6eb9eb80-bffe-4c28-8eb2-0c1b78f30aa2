{"name": "WebParserTool", "displayName": "网页内容解析工具", "version": "1.0.0", "description": "智能网页内容解析工具。能够解析网页链接，提取核心文本内容，支持JavaScript渲染页面，自动清理广告和无关内容，集成token限制管理。适用于网页内容分析、信息提取、内容整理等场景。", "pluginType": "synchronous", "entryPoint": {"command": "node WebParserTool.js"}, "communication": {"protocol": "stdio"}, "configSchema": {"WEB_PARSER_TIMEOUT": {"type": "number", "description": "网页请求超时时间（毫秒）", "required": false, "default": 30000}, "WEB_PARSER_MAX_CONTENT_LENGTH": {"type": "number", "description": "最大内容长度（字符）", "required": false, "default": 50000}, "WEB_PARSER_MAX_TOKENS": {"type": "number", "description": "最大token数量限制", "required": false, "default": 8000}, "WEB_PARSER_USER_AGENT": {"type": "string", "description": "HTTP用户代理字符串", "required": false, "default": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 WebParserTool/1.0.0"}, "WEB_PARSER_ENABLE_TOKEN_LIMIT": {"type": "boolean", "description": "是否启用token限制", "required": false, "default": true}, "WEB_PARSER_CONTENT_QUALITY": {"type": "string", "description": "内容质量级别", "required": false, "default": "high"}, "WEB_PARSER_USE_PROXY": {"type": "boolean", "description": "是否使用代理服务器", "required": false, "default": false}, "WEB_PARSER_PROXY_SERVER": {"type": "string", "description": "代理服务器地址", "required": false, "default": ""}, "DebugMode": {"type": "boolean", "description": "是否启用调试模式", "required": false, "default": false}}, "capabilities": {"systemPromptPlaceholders": [], "invocationCommands": [{"commandIdentifier": "WebParserTool", "description": "调用此工具解析网页内容。智能提取网页核心文本内容，自动清理JavaScript代码、广告和无关内容，支持token限制管理。请在您的回复中，使用以下精确格式来请求，确保所有参数值都用「始」和「末」准确包裹：\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」WebParserTool「末」,\nurl:「始」要解析的网页URL地址「末」\n<<<[END_TOOL_REQUEST]>>>\n\n重要提示给AI：\n当此工具执行完毕后，您将收到包含网页内容的详细信息，包括标题、内容、token使用情况等。请基于这些结果向用户展示解析的网页内容。", "example": "```text\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」WebParserTool「末」,\nurl:「始」https://example.com「末」\n<<<[END_TOOL_REQUEST]>>>\n```"}]}, "dependencies": {"node": ">=14.0.0", "npm_packages": ["axios", "puppeteer"]}, "features": ["智能网页内容提取", "JavaScript渲染支持", "垃圾内容自动清理", "Token限制管理", "多重备用方案", "中文内容优化"], "tags": ["网页解析", "内容提取", "文本清理", "Token管理", "智能过滤"], "author": "VCPToolBox", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-repo/vcptoolbox"}, "keywords": ["web-parser", "content-extraction", "text-cleaning", "token-management", "intelligent-filtering"]}