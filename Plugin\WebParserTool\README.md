# WebParserTool - 网页内容解析工具

## 📖 插件简介

WebParserTool 是一个智能的网页内容解析工具，能够从网页链接中提取核心文本内容。该插件支持JavaScript渲染页面，自动清理广告和无关内容，为用户提供干净、结构化的网页文本信息。

## ✨ 主要功能

- 🌐 **智能内容提取**: 自动识别网页主要内容区域
- 🤖 **JavaScript支持**: 使用Puppeteer处理动态渲染页面
- 🧹 **内容清理**: 自动移除广告、导航、页脚等无关内容
- 🔄 **多重备用**: Puppeteer失败时自动切换到axios备用方案
- 🌍 **中文优化**: 完美支持中文网页内容处理
- 📊 **元数据提取**: 提取标题、描述、关键词等元信息

## 🚀 快速开始

### 环境要求

- Node.js >= 14.0.0
- 内存 >= 256MB
- 磁盘空间 >= 50MB

### 安装依赖

```bash
npm install axios puppeteer
```

### 配置设置

1. 复制配置文件模板：
```bash
cp config.env.example config.env
```

2. 根据需要修改 `config.env` 文件中的配置。

## 📋 使用方法

### VCP插件调用

```bash
echo '{"url":"https://example.com"}' | node WebParserTool.js
```

### MCP插件调用

通过MCP服务器调用：
```javascript
const result = await mcpClient.callTool('WebParserTool', {
    url: 'https://example.com'
});
```

## 📊 输入参数

| 参数名 | 类型 | 必需 | 描述 |
|--------|------|------|------|
| `url` | string | ✅ | 要解析的网页URL |

## 📤 输出格式

```json
{
  "status": "success",
  "message": "网页内容解析完成",
  "data": {
    "url": "https://example.com",
    "title": "网页标题",
    "content": "提取的文本内容...",
    "content_length": 1500,
    "metadata": {
      "description": "网页描述",
      "keywords": "关键词"
    },
    "method": "puppeteer",
    "timestamp": "2024-01-01T00:00:00.000Z",
    "markdown_display": "# 网页标题\n\n**URL:** https://example.com\n\n..."
  }
}
```

## 🔧 配置选项

### 环境变量

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| `WEB_PARSER_TIMEOUT` | 请求超时时间（毫秒） | 30000 |
| `WEB_PARSER_MAX_CONTENT_LENGTH` | 最大内容长度（字符） | 50000 |
| `WEB_PARSER_USER_AGENT` | HTTP用户代理字符串 | Chrome/********* |

## 🛠️ 工作原理

1. **URL处理**: 自动添加协议前缀，验证URL格式
2. **内容获取**: 
   - 优先使用Puppeteer处理JavaScript渲染页面
   - 失败时自动切换到axios备用方案
3. **内容清理**: 
   - 移除脚本、样式、广告等无关内容
   - 智能识别主要内容区域
   - 格式化文本输出
4. **元数据提取**: 提取标题、描述、关键词等信息

## 🎯 支持的网站类型

- ✅ 静态HTML网站
- ✅ JavaScript渲染的SPA应用
- ✅ 新闻网站和博客
- ✅ 文档和知识库网站
- ✅ 电商和企业网站
- ✅ 社交媒体页面（部分）

## 🚨 常见问题

### 1. Puppeteer安装失败
```bash
# 设置Puppeteer下载镜像
npm config set puppeteer_download_host=https://npm.taobao.org/mirrors
npm install puppeteer
```

### 2. 网页内容获取失败
- 检查网络连接
- 确认目标网站是否可访问
- 某些网站可能有反爬虫机制

### 3. 内容提取不完整
- 部分网站使用复杂的JavaScript加载内容
- 可以尝试增加超时时间
- 某些网站可能需要登录才能访问完整内容

### 4. 性能问题
- Puppeteer启动较慢，首次使用需要下载Chromium
- 可以通过配置减少超时时间
- 对于简单网站，axios备用方案更快

## 📝 使用示例

### 解析新闻网站
```bash
echo '{"url":"https://www.bbc.com/news"}' | node WebParserTool.js
```

### 解析技术文档
```bash
echo '{"url":"https://nodejs.org/en/docs/"}' | node WebParserTool.js
```

### 解析中文网站
```bash
echo '{"url":"https://www.zhihu.com"}' | node WebParserTool.js
```

## 🔒 隐私和安全

- 本插件仅提取公开可访问的网页内容
- 不存储任何用户数据或网页内容
- 遵循网站的robots.txt规则
- 建议在使用前确认目标网站的使用条款

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个插件！

## 📞 支持

如果您在使用过程中遇到问题，请：
1. 查看本文档的常见问题部分
2. 检查插件日志输出
3. 提交Issue描述具体问题
