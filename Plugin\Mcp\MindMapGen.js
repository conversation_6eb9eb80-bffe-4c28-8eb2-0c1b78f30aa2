// Plugin/Mcp/MindMapGen.js - 思维导图生成MCP插件
const BaseMcpPlugin = require('./BaseMcpPlugin');

class MindMapGenMcp extends BaseMcpPlugin {
    constructor() {
        super();
        this.name = 'MindMapGen';
        this.description = '生成思维导图并渲染为图片发送。支持多种样式主题，可自定义图片尺寸。适用于知识梳理、项目规划、学习笔记等场景，用户需要思维导图或者构建相关的时候使用';
        this.vcpName = 'MindMapGen';
    }

    getParameters() {
        return {
            type: 'object',
            properties: {
                prompt: {
                    type: 'string',
                    description: '思维导图内容描述，详细描述要生成的思维导图主题和结构',
                    minLength: 1,
                    maxLength: 4000
                },
                width: {
                    type: 'number',
                    description: '输出图片宽度（像素）',
                    minimum: 2400,
                    maximum: 4000,
                    default: 3000
                },
                height: {
                    type: 'number',
                    description: '输出图片高度（像素）',
                    minimum: 1600,
                    maximum: 3000,
                    default: 2000
                },
                waitTime: {
                    type: 'number',
                    description: '渲染等待时间（毫秒），用于确保图片完全渲染',
                    minimum: 8000,
                    maximum: 30000,
                    default: 12000
                },
                style: {
                    type: 'string',
                    description: '思维导图样式主题，AI根据内容自动选择最合适的风格',
                    enum: [
                        'default',      // 经典白色背景，适合正式文档
                        'colorful',     // 彩色渐变背景，视觉丰富
                        'dark',         // 深色主题，适合技术内容
                        'minimal',      // 简约风格，突出内容
                        'anime',        // 动漫风格，可爱配色
                        'cyberpunk',    // 赛博朋克风格，科技感
                        'nature',       // 自然风格，绿色主题
                        'business',     // 商务风格，专业配色
                        'code',         // 代码风格，程序员友好
                        'academic',     // 学术风格，适合研究
                        'creative',     // 创意风格，艺术感
                        'retro'         // 复古风格，怀旧配色
                    ]
                }
            },
            required: ['prompt', 'style']
        };
    }

    async execute(args) {
        // 验证参数
        this.validateArgs(args);

        // 设置默认值
        if (!args.width) args.width = 3000;
        if (!args.height) args.height = 2000;
        if (!args.waitTime) args.waitTime = 12000;

        // AI根据内容自动选择样式
        if (!args.style) {
            args.style = this.selectStyleByContent(args.prompt);
            this.log('info', `AI自动选择样式: ${args.style}`);
        }

        this.log('info', `开始生成思维导图`, {
            prompt: args.prompt.substring(0, 100) + (args.prompt.length > 100 ? '...' : ''),
            width: args.width,
            height: args.height,
            style: args.style
        });

        try {
            // 调用对应的VCP插件
            const result = await this.callVcpPlugin(args);

            // VCP插件现在返回JSON格式
            let parsedResult;
            try {
                parsedResult = typeof result === 'string' ? JSON.parse(result) : result;
            } catch (e) {
                // 如果解析失败，可能是旧格式的字符串
                this.log('warning', `VCP插件返回格式异常，尝试兼容处理: ${e.message}`);

                if (typeof result === 'string') {
                    if (result.includes('失败') || result.includes('错误')) {
                        return {
                            type: 'mindmap_generation',
                            status: 'error',
                            message: result,
                            data: {
                                prompt: args.prompt,
                                error_details: result,
                                timestamp: new Date().toISOString()
                            }
                        };
                    }

                    return {
                        type: 'mindmap_generation',
                        status: 'success',
                        message: '思维导图生成完成',
                        data: {
                            prompt: args.prompt,
                            width: args.width,
                            height: args.height,
                            style: args.style,
                            markdown_display: result,
                            generation_info: {
                                model: 'gpt-4o-mini',
                                timestamp: new Date().toISOString(),
                                plugin_version: '1.0.0'
                            }
                        }
                    };
                }
            }

            // 处理标准JSON格式返回
            if (parsedResult.status === 'error') {
                this.log('error', `思维导图生成失败: ${parsedResult.message}`);

                return {
                    type: 'mindmap_generation',
                    status: 'error',
                    message: parsedResult.message,
                    data: {
                        prompt: args.prompt,
                        error_type: parsedResult.data?.error_type || 'UnknownError',
                        error_details: parsedResult.data?.error_details || parsedResult.message,
                        timestamp: parsedResult.data?.timestamp || new Date().toISOString()
                    }
                };
            }

            // 成功情况
            this.log('success', `思维导图生成成功`);

            return {
                type: 'mindmap_generation',
                status: 'success',
                message: parsedResult.message || '思维导图生成完成',
                data: {
                    prompt: args.prompt,
                    width: args.width,
                    height: args.height,
                    style: args.style,
                    markdown_content: parsedResult.data?.markdown_content,
                    image_path: parsedResult.data?.image_path,
                    image_url: parsedResult.data?.image_url,
                    markdown_display: parsedResult.data?.markdown_display,
                    generation_info: {
                        model: parsedResult.data?.generation_info?.model || 'gpt-4o-mini',
                        timestamp: parsedResult.data?.generation_info?.timestamp || new Date().toISOString(),
                        plugin_version: parsedResult.data?.generation_info?.plugin_version || '1.0.0'
                    }
                }
            };

        } catch (error) {
            this.log('error', `思维导图生成失败: ${error.message}`);
            
            return {
                type: 'mindmap_generation',
                status: 'error',
                message: `思维导图生成失败: ${error.message}`,
                data: {
                    prompt: args.prompt,
                    error_type: error.name || 'UnknownError',
                    error_details: error.message,
                    timestamp: new Date().toISOString()
                }
            };
        }
    }



    // 重写初始化方法
    async initialize() {
        await super.initialize();
        
        // 检查VCP插件是否可用
        try {
            const pluginManager = global.pluginManager;
            if (!pluginManager.getPlugin(this.vcpName)) {
                throw new Error(`找不到对应的VCP插件: ${this.vcpName}`);
            }
            
            // 检查必要的环境变量
            const requiredEnvVars = ['OPENAI_API_KEY'];
            for (const envVar of requiredEnvVars) {
                if (!process.env[envVar]) {
                    this.log('warning', `未配置${envVar}环境变量，可能影响AI生成功能`);
                }
            }
        } catch (error) {
            this.log('error', `插件初始化失败: ${error.message}`);
            throw error;
        }
        
        return true;
    }

    /**
     * AI根据内容自动选择合适的样式
     * @param {string} prompt - 用户输入的提示内容
     * @returns {string} - 选择的样式名称
     */
    selectStyleByContent(prompt) {
        const content = prompt.toLowerCase();

        // 定义关键词和对应的样式
        const styleRules = [
            {
                keywords: ['动漫', '二次元', '可爱', '萌', 'anime', 'kawaii', '卡通'],
                style: 'anime',
                description: '动漫风格'
            },
            {
                keywords: ['代码', '编程', '算法', '技术', 'code', 'programming', 'api', '开发', '软件'],
                style: 'code',
                description: '代码风格'
            },
            {
                keywords: ['科技', '未来', '人工智能', 'ai', '机器学习', '区块链', '赛博', 'cyber'],
                style: 'cyberpunk',
                description: '赛博朋克风格'
            },
            {
                keywords: ['商业', '企业', '管理', '营销', '商务', 'business', '公司', '战略'],
                style: 'business',
                description: '商务风格'
            },
            {
                keywords: ['学术', '研究', '论文', '理论', '科学', 'academic', '教育', '学习'],
                style: 'academic',
                description: '学术风格'
            },
            {
                keywords: ['自然', '环保', '生态', '植物', '动物', 'nature', '绿色', '环境'],
                style: 'nature',
                description: '自然风格'
            },
            {
                keywords: ['创意', '艺术', '设计', '创作', 'creative', '灵感', '想象'],
                style: 'creative',
                description: '创意风格'
            },
            {
                keywords: ['复古', '怀旧', '经典', 'retro', 'vintage', '传统', '历史'],
                style: 'retro',
                description: '复古风格'
            },
            {
                keywords: ['黑暗', '深色', '夜晚', 'dark', '暗色', '黑色'],
                style: 'dark',
                description: '深色主题'
            },
            {
                keywords: ['简约', '简单', 'minimal', '极简', '清爽'],
                style: 'minimal',
                description: '简约风格'
            }
        ];

        // 检查内容匹配的关键词
        for (const rule of styleRules) {
            for (const keyword of rule.keywords) {
                if (content.includes(keyword)) {
                    this.log('debug', `匹配关键词"${keyword}"，选择${rule.description}`);
                    return rule.style;
                }
            }
        }

        // 根据内容长度和复杂度选择
        if (content.length > 200) {
            return 'academic'; // 长内容使用学术风格
        } else if (content.includes('?') || content.includes('？')) {
            return 'colorful'; // 问题类内容使用彩色风格
        } else {
            return 'default'; // 默认风格
        }
    }
}

module.exports = MindMapGenMcp;
