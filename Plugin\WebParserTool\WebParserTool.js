#!/usr/bin/env node
// Plugin/WebParserTool/WebParserTool.js - 网页内容解析VCP插件
const axios = require('axios');
const dotenv = require('dotenv');
const path = require('path');

// 引入token工具
let tokenUtils;
try {
    const { tokenUtils: tu } = require('../../utils/tokenUtils.js');
    tokenUtils = tu;
} catch (e) {
    console.error('无法加载tokenUtils，将使用简单字符截断:', e.message);
    tokenUtils = null;
}

// 引入VCP日志系统，强制输出到stderr以避免污染stdout的JSON
let logger;
try {
    const loggerPath = process.env.NODE_ENV === 'production' 
        ? '../../utils/logger.cjs' 
        : '../../utils/logger.js';
    const vcpLogger = require(loggerPath).default || require(loggerPath);
    
    // 为JSON输出插件创建特殊的logger，强制输出到stderr
    logger = {
        info: (component, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [i] [${component}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        },
        error: (component, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [x] [${component}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        },
        warning: (component, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [!] [${component}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        },
        debug: (component, msg, data) => {
            if (process.env.DebugMode === 'true') {
                const output = `[${new Date().toLocaleString('zh-CN')}] [*] [${component}] ${msg}`;
                console.error(output);
                if (data) console.error(data);
            }
        },
        success: (component, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [✓] [${component}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        }
    };
} catch (e) {
    // 回退到传统日志，也输出到stderr
    logger = {
        info: (component, msg, data) => console.error(`[INFO] [${component}] ${msg}`, data || ''),
        error: (component, msg, data) => console.error(`[ERROR] [${component}] ${msg}`, data || ''),
        warning: (component, msg, data) => console.error(`[WARN] [${component}] ${msg}`, data || ''),
        debug: (component, msg, data) => {
            if (process.env.DebugMode === 'true') {
                console.error(`[DEBUG] [${component}] ${msg}`, data || '');
            }
        },
        success: (component, msg, data) => console.error(`[SUCCESS] [${component}] ${msg}`, data || '')
    };
}

// 动态导入puppeteer
let puppeteer;
try {
    puppeteer = require('puppeteer');
} catch (e) {
    logger.warning('网页解析工具', 'Puppeteer未安装，将使用axios备用方案');
}

/**
 * 加载插件配置
 */
function loadPluginConfig() {
    const pluginConfigPath = path.join(__dirname, 'config.env');
    let pluginConfig = {};

    // 加载插件配置
    try {
        const configContent = require('fs').readFileSync(pluginConfigPath, 'utf8');
        pluginConfig = parseEnvConfig(configContent);
    } catch (error) {
        // 插件配置不存在，使用主服务器配置
    }

    // 加载主服务器配置作为备用
    dotenv.config({ path: path.resolve(__dirname, '../../config.env') });

    // 默认配置
    const defaultConfig = {
        WEB_PARSER_TIMEOUT: '30000',
        WEB_PARSER_MAX_CONTENT_LENGTH: '50000',
        WEB_PARSER_MAX_TOKENS: '8000',
        WEB_PARSER_USER_AGENT: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36 WebParserTool/1.0.0',
        WEB_PARSER_ENABLE_TOKEN_LIMIT: 'true',
        WEB_PARSER_CONTENT_QUALITY: 'high',
        DebugMode: 'false'
    };

    // 配置优先级：插件配置 > 主服务器环境变量 > 默认值
    const mergedConfig = {
        WEB_PARSER_TIMEOUT: pluginConfig.WEB_PARSER_TIMEOUT || process.env.WEB_PARSER_TIMEOUT || defaultConfig.WEB_PARSER_TIMEOUT,
        WEB_PARSER_MAX_CONTENT_LENGTH: pluginConfig.WEB_PARSER_MAX_CONTENT_LENGTH || process.env.WEB_PARSER_MAX_CONTENT_LENGTH || defaultConfig.WEB_PARSER_MAX_CONTENT_LENGTH,
        WEB_PARSER_MAX_TOKENS: pluginConfig.WEB_PARSER_MAX_TOKENS || process.env.WEB_PARSER_MAX_TOKENS || defaultConfig.WEB_PARSER_MAX_TOKENS,
        WEB_PARSER_USER_AGENT: pluginConfig.WEB_PARSER_USER_AGENT || process.env.WEB_PARSER_USER_AGENT || defaultConfig.WEB_PARSER_USER_AGENT,
        WEB_PARSER_ENABLE_TOKEN_LIMIT: pluginConfig.WEB_PARSER_ENABLE_TOKEN_LIMIT || process.env.WEB_PARSER_ENABLE_TOKEN_LIMIT || defaultConfig.WEB_PARSER_ENABLE_TOKEN_LIMIT,
        WEB_PARSER_CONTENT_QUALITY: pluginConfig.WEB_PARSER_CONTENT_QUALITY || process.env.WEB_PARSER_CONTENT_QUALITY || defaultConfig.WEB_PARSER_CONTENT_QUALITY,
        DebugMode: pluginConfig.DebugMode || process.env.DebugMode || defaultConfig.DebugMode
    };

    return mergedConfig;
}

/**
 * 解析.env格式的配置文件
 */
function parseEnvConfig(content) {
    const config = {};
    content.split('\n').forEach(line => {
        line = line.trim();
        if (line && !line.startsWith('#')) {
            const equalIndex = line.indexOf('=');
            if (equalIndex > 0) {
                const key = line.substring(0, equalIndex).trim();
                const value = line.substring(equalIndex + 1).trim().replace(/^["']|["']$/g, '');
                config[key] = value;
            }
        }
    });
    return config;
}

// 加载配置
const config = loadPluginConfig();

// 插件配置
const pluginConfig = {
    timeout: parseInt(config.WEB_PARSER_TIMEOUT),
    maxContentLength: parseInt(config.WEB_PARSER_MAX_CONTENT_LENGTH),
    maxTokens: parseInt(config.WEB_PARSER_MAX_TOKENS),
    userAgent: config.WEB_PARSER_USER_AGENT,
    enableTokenLimit: config.WEB_PARSER_ENABLE_TOKEN_LIMIT === 'true',
    contentQuality: config.WEB_PARSER_CONTENT_QUALITY,
    debugMode: config.DebugMode === 'true',
    // 新增配置项
    proxyServer: config.WEB_PARSER_PROXY_SERVER || '',
    useProxy: config.WEB_PARSER_USE_PROXY === 'true',
    maxRetries: 3,
    retryDelay: 3000
};

if (pluginConfig.debugMode) {
    logger.debug('网页解析工具', '插件配置:', pluginConfig);
}

/**
 * 处理URL，确保格式正确
 * @param {string} url - 输入的URL
 * @returns {string|null} - 处理后的URL或null
 */
function processUrl(url) {
    try {
        // 移除首尾空格
        url = url.trim();

        // 如果不包含协议，添加https://
        if (!/^https?:\/\//i.test(url)) {
            url = 'https://' + url;
        }

        // 验证URL格式
        new URL(url);
        return url;
    } catch (error) {
        return null;
    }
}

/**
 * 计算两个字符串的相似度
 * @param {string} str1 - 第一个字符串
 * @param {string} str2 - 第二个字符串
 * @returns {number} - 返回相似度(0-1之间)
 */
function calculateSimilarity(str1, str2) {
    const len1 = str1.length;
    const len2 = str2.length;
    const matrix = Array(len1 + 1).fill().map(() => Array(len2 + 1).fill(0));

    // 初始化矩阵
    for (let i = 0; i <= len1; i++) matrix[i][0] = i;
    for (let j = 0; j <= len2; j++) matrix[0][j] = j;

    // 计算Levenshtein距离
    for (let i = 1; i <= len1; i++) {
        for (let j = 1; j <= len2; j++) {
            const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
            matrix[i][j] = Math.min(
                matrix[i - 1][j] + 1,      // 删除
                matrix[i][j - 1] + 1,      // 插入
                matrix[i - 1][j - 1] + cost // 替换
            );
        }
    }

    // 计算相似度
    return 1 - matrix[len1][len2] / Math.max(len1, len2);
}

/**
 * 使用Puppeteer获取网页内容
 * @param {string} url - 网页URL
 * @returns {Promise<Object>} - 网页内容对象
 */
async function fetchWithPuppeteer(url) {
    const MAX_RETRIES = 3;
    const RETRY_DELAY = 3000;
    let browser;

    for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
        try {
            logger.debug('网页解析工具', `第 ${attempt} 次尝试启动Puppeteer`);

            // 更完善的浏览器启动配置
            const launchOptions = {
                headless: true,
                timeout: 60000,
                ignoreHTTPSErrors: true,
                ignoreDefaultArgs: ['--disable-extensions'],
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-gpu',
                    '--disable-software-rasterizer',
                    '--disable-background-timer-throttling',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-renderer-backgrounding',
                    '--disable-features=TranslateUI',
                    '--disable-ipc-flooding-protection',
                    '--enable-javascript',
                    '--window-size=1920,1080',
                    '--user-agent=' + pluginConfig.userAgent,
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--disable-blink-features=AutomationControlled',
                    '--no-first-run',
                    '--no-default-browser-check',
                    '--disable-default-apps',
                    '--disable-popup-blocking',
                    '--disable-translate',
                    '--disable-background-networking',
                    '--disable-sync',
                    '--metrics-recording-only',
                    '--disable-prompt-on-repost',
                    '--disable-hang-monitor',
                    '--disable-client-side-phishing-detection',
                    '--disable-component-update',
                    '--disable-domain-reliability',
                    // 网络相关优化
                    '--aggressive-cache-discard',
                    '--disable-background-timer-throttling',
                    '--disable-renderer-backgrounding',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-features=TranslateUI,BlinkGenPropertyTrees',
                    '--disable-extensions-http-throttling',
                    '--disable-component-extensions-with-background-pages'
                ]
            };

            // 代理配置
            if (pluginConfig.useProxy && pluginConfig.proxyServer) {
                launchOptions.args.push(`--proxy-server=${pluginConfig.proxyServer}`);
                logger.debug('网页解析工具', `使用代理服务器: ${pluginConfig.proxyServer}`);
            }

            // 如果是Windows系统，添加特定参数
            if (process.platform === 'win32') {
                launchOptions.args.push(
                    '--disable-features=VizDisplayCompositor',
                    '--disable-gpu-sandbox'
                );
            }

            // 如果是Linux系统，添加特定参数
            if (process.platform === 'linux') {
                launchOptions.args.push(
                    '--disable-dev-shm-usage',
                    '--no-zygote',
                    '--single-process'
                );
            }

            // 启动浏览器
            browser = await puppeteer.launch(launchOptions);

            const page = await browser.newPage();

            //logger.debug('网页解析工具', '浏览器页面创建成功');

            // 设置视口大小
            await page.setViewport({ width: 1920, height: 1080 });

            // 设置用户代理和HTTP头
            await page.setUserAgent(pluginConfig.userAgent);
            await page.setExtraHTTPHeaders({
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-US;q=0.7',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Upgrade-Insecure-Requests': '1'
            });

            // 设置导航和请求超时时间
            await page.setDefaultNavigationTimeout(pluginConfig.timeout);
            await page.setDefaultTimeout(pluginConfig.timeout);

            // 设置请求拦截，优化加载性能并处理错误
            await page.setRequestInterception(true);
            page.on('request', request => {
                const resourceType = request.resourceType();
                const url = request.url();

                // 阻止不必要的资源加载
                if (['image', 'font', 'media', 'stylesheet'].includes(resourceType)) {
                    request.abort();
                } else if (url.includes('analytics') || url.includes('tracking') || url.includes('ads')) {
                    request.abort();
                } else {
                    request.continue();
                }
            });

            // 处理页面错误
            page.on('error', err => {
                logger.warning('网页解析工具', `页面错误: ${err.message}`);
            });

            page.on('pageerror', err => {
                logger.warning('网页解析工具', `页面JavaScript错误: ${err.message}`);
            });

            //logger.debug('网页解析工具', `开始访问URL: ${url}`);

            // 导航到页面并等待加载完成，使用更灵活的等待策略
            try {
                await page.goto(url, {
                    waitUntil: ['domcontentloaded'],
                    timeout: pluginConfig.timeout
                });

                //logger.debug('网页解析工具', 'DOM内容加载完成');

                // 等待网络空闲或超时（Puppeteer方式）
                try {
                    // 尝试等待网络空闲
                    await page.waitForFunction(() => {
                        return document.readyState === 'complete';
                    }, { timeout: 8000 });
                    //logger.debug('网页解析工具', '页面完全加载完成');
                } catch (e) {
                    //logger.debug('网页解析工具', '页面加载等待超时，继续处理');
                }

                // 额外等待让动态内容加载
                await new Promise(resolve => setTimeout(resolve, 3000));

            } catch (gotoError) {
                logger.warning('网页解析工具', `页面导航失败: ${gotoError.message}`);

                // 尝试简单的导航
                await page.goto(url, {
                    waitUntil: ['domcontentloaded'],
                    timeout: pluginConfig.timeout * 0.8
                });
            }

            // 等待页面JavaScript执行完成
            await new Promise(resolve => setTimeout(resolve, 3000));

            // 等待body元素确保存在
            await page.waitForSelector('body', { timeout: 5000 }).catch(() => {
                logger.warning('网页解析工具', 'body元素等待超时，继续执行');
            });

            // 针对微信公众号等特殊页面，等待内容加载
            if (url.includes('mp.weixin.qq.com')) {
                logger.debug('网页解析工具', '检测到微信公众号页面，等待内容加载');
                await page.waitForSelector('#js_content, .rich_media_content', { timeout: 10000 }).catch(() => {
                    logger.warning('网页解析工具', '微信内容元素等待超时');
                });
                // 额外等待JavaScript渲染
                await new Promise(resolve => setTimeout(resolve, 5000));
            }

            //logger.debug('网页解析工具', '页面加载完成，开始内容提取');

            // 检查页面是否正常加载
            const pageStatus = await page.evaluate(() => {
                // 确保document.body存在
                if (!document.body) {
                    return {
                        hasJsError: false,
                        hasNetworkError: true,
                        hasBlockedContent: false,
                        isEmpty: true,
                        textLength: 0,
                        title: document.title || '',
                        error: 'document.body不存在'
                    };
                }

                const text = document.body.innerText || document.body.textContent || '';
                const title = document.title || '';

                // 检查各种错误情况
                const hasJsError = text.includes("doesn't work properly without JavaScript enabled") ||
                    text.includes("请启用JavaScript") ||
                    text.includes("需要启用JavaScript") ||
                    text.includes("JavaScript is required");

                const hasNetworkError = text.includes("ERR_") ||
                    text.includes("网络错误") ||
                    text.includes("连接超时") ||
                    text.includes("无法访问") ||
                    title.includes("错误");

                const hasBlockedContent = text.includes("访问被拒绝") ||
                    text.includes("403") ||
                    text.includes("Forbidden") ||
                    text.includes("Access Denied");

                const isEmpty = text.trim().length < 50;

                return {
                    hasJsError,
                    hasNetworkError,
                    hasBlockedContent,
                    isEmpty,
                    textLength: text.length,
                    title: title
                };
            });

            if (pageStatus.hasJsError) {
                throw new Error('检测到JavaScript禁用提示，重试加载');
            }

            if (pageStatus.hasNetworkError) {
                throw new Error('检测到网络错误页面');
            }

            if (pageStatus.hasBlockedContent) {
                throw new Error('检测到访问被拒绝页面');
            }

            if (pageStatus.isEmpty) {
                throw new Error('页面内容为空或过少');
            }

            //logger.debug('网页解析工具', `页面状态检查通过，标题: ${pageStatus.title}, 内容长度: ${pageStatus.textLength}`);

            // 智能提取页面内容
            const content = await page.evaluate((contentQuality) => {
                /**
                 * 智能内容提取算法
                 */

                // 安全检查：确保document.body存在
                if (!document.body) {
                    return {
                        textContent: '',
                        title: document.title || '',
                        meta: {
                            description: '',
                            keywords: ''
                        },
                        contentScore: 0,
                        extractionMethod: 'error-no-body'
                    };
                }

                // 垃圾内容选择器 - 需要移除的元素
                const JUNK_SELECTORS = [
                    'script', 'style', 'noscript', 'iframe', 'embed', 'object',
                    'nav', 'header', 'footer', 'aside', 'menu',
                    '.ads', '.ad', '.advertisement', '.banner', '.popup',
                    '[class*="ads"]', '[id*="ads"]', '[class*="ad-"]', '[id*="ad-"]',
                    '[class*="advertisement"]', '[id*="advertisement"]',
                    '[class*="banner"]', '[id*="banner"]',
                    '[class*="popup"]', '[id*="popup"]',
                    '[class*="modal"]', '[id*="modal"]',
                    '[class*="overlay"]', '[id*="overlay"]',
                    '[class*="sidebar"]', '[id*="sidebar"]',
                    '[class*="widget"]', '[id*="widget"]',
                    '[class*="social"]', '[id*="social"]',
                    '[class*="share"]', '[id*="share"]',
                    '[class*="comment"]', '[id*="comment"]',
                    '[class*="related"]', '[id*="related"]',
                    '[class*="recommend"]', '[id*="recommend"]',
                    '[class*="tracking"]', '[id*="tracking"]',
                    '[class*="analytics"]', '[id*="analytics"]',
                    '.hidden', '[style*="display:none"]', '[style*="visibility:hidden"]',
                    '[aria-hidden="true"]', '.sr-only', '.visually-hidden'
                ];

                // 高质量内容选择器 - 优先提取的内容区域
                const CONTENT_SELECTORS = [
                    // 微信公众号特定选择器
                    '#js_content', '.rich_media_content', '#img-content',
                    '.rich_media_area_primary', '.rich_media_area_primary_inner',
                    // 通用文章选择器
                    'article',
                    '[role="main"]',
                    '.main-content', '.content-main', '.page-content',
                    '.article-content', '.post-content', '.entry-content',
                    '.article-body', '.post-body', '.entry-body',
                    '#content', '#main-content', '#article-content',
                    '.articleContent', '#newsText', '.news-content',
                    '.markdown-body', '.wiki-content', '#wiki_panel',
                    '.repository-content', '.blob-wrapper',
                    '.text-content', '.main-text', '.content-text',
                    // 更多备用选择器
                    '.content', '.post', '.article', '.entry',
                    '[class*="content"]', '[class*="article"]', '[class*="post"]'
                ];

                // 移除垃圾元素
                function removeJunkElements() {
                    JUNK_SELECTORS.forEach(selector => {
                        try {
                            const elements = document.querySelectorAll(selector);
                            elements.forEach(el => {
                                if (el && el.parentNode) {
                                    el.parentNode.removeChild(el);
                                }
                            });
                        } catch (e) {
                            // 忽略选择器错误
                        }
                    });
                }

                // 计算元素的内容质量分数
                function calculateContentScore(element) {
                    if (!element || !element.textContent) return 0;

                    const text = element.textContent.trim();
                    const textLength = text.length;

                    if (textLength < 50) return 0;

                    let score = textLength;

                    // 段落数量加分
                    const paragraphs = element.querySelectorAll('p').length;
                    score += paragraphs * 50;

                    // 链接密度惩罚
                    const links = element.querySelectorAll('a');
                    const linkText = Array.from(links).reduce((sum, link) => sum + (link.textContent || '').length, 0);
                    const linkDensity = linkText / textLength;
                    if (linkDensity > 0.3) score *= (1 - linkDensity);

                    // JavaScript代码惩罚
                    const jsPatterns = [
                        /function\s*\(/g,
                        /var\s+\w+\s*=/g,
                        /const\s+\w+\s*=/g,
                        /let\s+\w+\s*=/g,
                        /\.\w+\(/g,
                        /\w+\.\w+/g
                    ];

                    let jsMatches = 0;
                    jsPatterns.forEach(pattern => {
                        const matches = text.match(pattern);
                        if (matches) jsMatches += matches.length;
                    });

                    if (jsMatches > 10) {
                        score *= Math.max(0.1, 1 - (jsMatches / textLength * 100));
                    }

                    // CSS样式代码惩罚
                    const cssPatterns = [
                        /\w+\s*:\s*[^;]+;/g,
                        /\.\w+\s*{/g,
                        /#\w+\s*{/g
                    ];

                    let cssMatches = 0;
                    cssPatterns.forEach(pattern => {
                        const matches = text.match(pattern);
                        if (matches) cssMatches += matches.length;
                    });

                    if (cssMatches > 5) {
                        score *= Math.max(0.1, 1 - (cssMatches / textLength * 50));
                    }

                    // 重复字符惩罚
                    const repeatedChars = text.match(/(.)\1{10,}/g);
                    if (repeatedChars) {
                        score *= 0.5;
                    }

                    return score;
                }

                // 清理文本内容
                function cleanText(text) {
                    if (!text) return '';

                    return text
                        // 移除多余的空白字符
                        .replace(/\s+/g, ' ')
                        // 移除JavaScript代码片段
                        .replace(/function\s*\([^)]*\)\s*\{[^}]*\}/g, '')
                        .replace(/\w+\.\w+\([^)]*\)/g, '')
                        // 移除CSS样式
                        .replace(/\w+\s*:\s*[^;]+;/g, '')
                        .replace(/\.\w+\s*\{[^}]*\}/g, '')
                        // 移除HTML实体
                        .replace(/&nbsp;/g, ' ')
                        .replace(/&amp;/g, '&')
                        .replace(/&lt;/g, '<')
                        .replace(/&gt;/g, '>')
                        .replace(/&quot;/g, '"')
                        .replace(/&#39;/g, "'")
                        // 移除重复的标点符号
                        .replace(/[.]{3,}/g, '...')
                        .replace(/[-]{3,}/g, '---')
                        .replace(/[=]{3,}/g, '===')
                        // 移除过长的重复字符
                        .replace(/(.)\1{5,}/g, '$1$1$1')
                        // 清理行尾空格和多余换行
                        .split('\n')
                        .map(line => line.trim())
                        .filter(line => line.length > 0)
                        .join('\n')
                        .trim();
                }

                // 开始提取
                removeJunkElements();

                let bestElement = null;
                let bestScore = 0;
                let debugInfo = {
                    totalSelectors: CONTENT_SELECTORS.length,
                    foundElements: 0,
                    scoresChecked: 0
                };

                // 尝试使用内容选择器
                for (const selector of CONTENT_SELECTORS) {
                    try {
                        const elements = document.querySelectorAll(selector);
                        debugInfo.foundElements += elements.length;

                        for (const element of elements) {
                            const score = calculateContentScore(element);
                            debugInfo.scoresChecked++;

                            if (score > bestScore) {
                                bestScore = score;
                                bestElement = element;
                                debugInfo.bestSelector = selector;
                            }
                        }
                        // 如果找到了高质量内容，提前退出
                        if (bestScore > 1000) {
                            break;
                        }
                    } catch (e) {
                        // 忽略选择器错误
                    }
                }

                // 如果没有找到好的内容，尝试body的直接子元素
                if (!bestElement || bestScore < 100) {
                    if (document.body && document.body.children) {
                        const bodyChildren = Array.from(document.body.children);
                        for (const element of bodyChildren) {
                            const score = calculateContentScore(element);
                            if (score > bestScore) {
                                bestScore = score;
                                bestElement = element;
                            }
                        }
                    }
                }

                // 最后的备用方案
                if (!bestElement) {
                    bestElement = document.body;
                }

                // 最终安全检查
                if (!bestElement) {
                    return {
                        textContent: document.title || 'No content available',
                        title: document.title || '',
                        meta: {
                            description: '',
                            keywords: ''
                        },
                        contentScore: 0,
                        extractionMethod: 'fallback-no-element'
                    };
                }

                // 提取文本内容
                let textContent = bestElement.innerText || bestElement.textContent || '';
                textContent = cleanText(textContent);

                // 提取元数据
                const title = document.title || '';
                const description = document.querySelector('meta[name="description"]')?.content || '';
                const keywords = document.querySelector('meta[name="keywords"]')?.content || '';

                return {
                    textContent: textContent,
                    title: title,
                    meta: {
                        description: description,
                        keywords: keywords
                    },
                    contentScore: bestScore,
                    extractionMethod: bestElement.tagName + (bestElement.className ? '.' + bestElement.className.split(' ')[0] : ''),
                    debugInfo: debugInfo
                };
            }, pluginConfig.contentQuality);

            // 验证提取的内容
            if (!content.textContent || content.textContent.trim().length === 0) {
                throw new Error('提取的内容无效或为空');
            }

            logger.debug('网页解析工具', `内容提取完成，原始长度: ${content.textContent.length}, 质量分数: ${content.contentScore}, 提取方法: ${content.extractionMethod}`);

            // 应用token限制
            let finalText = content.textContent;
            let tokenInfo = null;

            if (pluginConfig.enableTokenLimit && tokenUtils) {
                try {
                    const result = tokenUtils.processPluginContent(finalText, 'WEB_PARSER');
                    finalText = result.content;
                    tokenInfo = {
                        originalTokens: result.originalTokens,
                        finalTokens: result.finalTokens,
                        truncated: result.truncated,
                        maxTokens: pluginConfig.maxTokens
                    };

                    if (result.truncated) {
                        logger.info('网页解析工具', `内容已截断，原始tokens: ${result.originalTokens}, 最终tokens: ${result.finalTokens}`);
                    }
                } catch (tokenError) {
                    logger.warning('网页解析工具', `Token处理失败: ${tokenError.message}`);
                }
            }

            // 如果没有token工具，使用字符长度限制
            if (!tokenInfo) {
                if (finalText.length > pluginConfig.maxContentLength) {
                    finalText = finalText.substring(0, pluginConfig.maxContentLength) + '\n\n[内容已截断，超过最大长度限制]';
                    tokenInfo = {
                        originalLength: content.textContent.length,
                        finalLength: finalText.length,
                        truncated: true,
                        maxLength: pluginConfig.maxContentLength
                    };
                }
            }

            // 构建最终返回的数据结构
            const finalContent = {
                title: content.title,
                text: finalText,
                metadata: {
                    ...content.meta,
                    contentScore: content.contentScore,
                    extractionMethod: content.extractionMethod
                },
                tokenInfo: tokenInfo,
                url: url,
                timestamp: new Date().toISOString(),
                method: 'puppeteer'
            };

            return finalContent;

        } catch (error) {
            logger.error('网页解析工具', `第 ${attempt} 次尝试失败:`, error.message);

            if (browser) {
                try {
                    await browser.close();
                } catch (closeError) {
                    logger.warning('网页解析工具', `关闭浏览器失败: ${closeError.message}`);
                }
                browser = null;
            }

            if (attempt === MAX_RETRIES) {
                // 根据错误类型提供更具体的错误信息
                let errorMessage = error.message;
                if (error.message.includes('ERR_CONNECTION_CLOSED')) {
                    errorMessage = '网络连接被关闭，可能是网络不稳定或目标网站拒绝连接';
                } else if (error.message.includes('ERR_NETWORK_CHANGED')) {
                    errorMessage = '网络环境发生变化，请检查网络连接';
                } else if (error.message.includes('ERR_INTERNET_DISCONNECTED')) {
                    errorMessage = '网络连接断开，请检查网络设置';
                } else if (error.message.includes('timeout')) {
                    errorMessage = '请求超时，目标网站响应过慢';
                } else if (error.message.includes('Navigation failed')) {
                    errorMessage = '页面导航失败，可能是URL无效或网站不可访问';
                }

                throw new Error(`在 ${MAX_RETRIES} 次尝试后仍然无法从 ${url} 获取内容: ${errorMessage}`);
            }

            // 递增重试延迟
            const currentDelay = RETRY_DELAY * attempt;
            //logger.info('网页解析工具', `等待 ${currentDelay}ms 后重试...`);
            await new Promise(resolve => setTimeout(resolve, currentDelay));
        } finally {
            if (browser) {
                try {
                    await browser.close();
                } catch (closeError) {
                    logger.warning('网页解析工具', `最终关闭浏览器失败: ${closeError.message}`);
                }
            }
        }
    }
}

/**
 * 使用axios获取网页内容（备用方案）
 * @param {string} url - 网页URL
 * @returns {Promise<Object>} - 网页内容对象
 */
async function fetchWithAxios(url) {
    try {
        logger.info('网页解析工具', `使用axios获取URL: ${url}`);

        const response = await axios.get(url, {
            headers: {
                'User-Agent': pluginConfig.userAgent,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            },
            timeout: pluginConfig.timeout,
            maxRedirects: 5
        });

        if (response.status !== 200) {
            throw new Error(`请求失败，状态码: ${response.status}`);
        }

        const contentType = response.headers['content-type'];
        if (!contentType || !contentType.includes('text/html')) {
            throw new Error(`不支持的内容类型: ${contentType}. 只支持 text/html。`);
        }

        // 智能HTML内容清理和提取
        const extractedContent = extractContentFromHtml(response.data);

        if (!extractedContent.text.trim()) {
            throw new Error('提取到的文本内容为空');
        }

        // 应用token限制
        let finalText = extractedContent.text;
        let tokenInfo = null;

        if (pluginConfig.enableTokenLimit && tokenUtils) {
            try {
                const result = tokenUtils.processPluginContent(finalText, 'WEB_PARSER');
                finalText = result.content;
                tokenInfo = {
                    originalTokens: result.originalTokens,
                    finalTokens: result.finalTokens,
                    truncated: result.truncated,
                    maxTokens: pluginConfig.maxTokens
                };
            } catch (tokenError) {
                logger.warning('网页解析工具', `Token处理失败: ${tokenError.message}`);
            }
        }

        // 如果没有token工具，使用字符长度限制
        if (!tokenInfo) {
            if (finalText.length > pluginConfig.maxContentLength) {
                finalText = finalText.substring(0, pluginConfig.maxContentLength) + '\n\n[内容已截断，超过最大长度限制]';
                tokenInfo = {
                    originalLength: extractedContent.text.length,
                    finalLength: finalText.length,
                    truncated: true,
                    maxLength: pluginConfig.maxContentLength
                };
            }
        }

        return {
            title: extractedContent.title,
            text: finalText,
            metadata: {
                description: extractedContent.description,
                keywords: extractedContent.keywords,
                contentScore: extractedContent.contentScore || 0,
                extractionMethod: 'axios-html-parser'
            },
            tokenInfo: tokenInfo,
            url: url,
            timestamp: new Date().toISOString(),
            method: 'axios'
        };

    } catch (error) {
        throw new Error(`axios获取失败: ${error.message}`);
    }
}

/**
 * 智能HTML内容提取（用于axios备用方案）
 * @param {string} html - HTML内容
 * @returns {Object} - 提取的内容对象
 */
function extractContentFromHtml(html) {
    // 提取标题
    const titleMatch = html.match(/<title[^>]*>([^<]*)<\/title>/i);
    const title = titleMatch ? titleMatch[1].trim() : '';

    // 提取meta信息
    const descMatch = html.match(/<meta[^>]*name=["']description["'][^>]*content=["']([^"']*)["'][^>]*>/i);
    const keywordsMatch = html.match(/<meta[^>]*name=["']keywords["'][^>]*content=["']([^"']*)["'][^>]*>/i);

    // 移除垃圾内容
    let cleanedHtml = html
        // 移除脚本和样式
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '')
        .replace(/<noscript\b[^<]*(?:(?!<\/noscript>)<[^<]*)*<\/noscript>/gi, '')
        // 移除注释
        .replace(/<!--[\s\S]*?-->/g, '')
        // 移除导航、页脚、侧边栏等
        .replace(/<nav\b[^<]*(?:(?!<\/nav>)<[^<]*)*<\/nav>/gi, '')
        .replace(/<header\b[^<]*(?:(?!<\/header>)<[^<]*)*<\/header>/gi, '')
        .replace(/<footer\b[^<]*(?:(?!<\/footer>)<[^<]*)*<\/footer>/gi, '')
        .replace(/<aside\b[^<]*(?:(?!<\/aside>)<[^<]*)*<\/aside>/gi, '')
        // 移除广告相关
        .replace(/<div[^>]*class[^>]*ad[^>]*>[\s\S]*?<\/div>/gi, '')
        .replace(/<div[^>]*id[^>]*ad[^>]*>[\s\S]*?<\/div>/gi, '');

    // 尝试提取主要内容区域
    const contentPatterns = [
        /<article[^>]*>([\s\S]*?)<\/article>/gi,
        /<div[^>]*class[^>]*content[^>]*>([\s\S]*?)<\/div>/gi,
        /<div[^>]*class[^>]*main[^>]*>([\s\S]*?)<\/div>/gi,
        /<main[^>]*>([\s\S]*?)<\/main>/gi
    ];

    let bestContent = '';
    let bestScore = 0;

    for (const pattern of contentPatterns) {
        const matches = cleanedHtml.match(pattern);
        if (matches) {
            for (const match of matches) {
                const textContent = match.replace(/<[^>]+>/g, ' ').trim();
                const score = calculateTextQuality(textContent);
                if (score > bestScore) {
                    bestScore = score;
                    bestContent = textContent;
                }
            }
        }
    }

    // 如果没有找到好的内容，使用整个body
    if (!bestContent) {
        const bodyMatch = cleanedHtml.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
        if (bodyMatch) {
            bestContent = bodyMatch[1].replace(/<[^>]+>/g, ' ').trim();
            bestScore = calculateTextQuality(bestContent);
        }
    }

    // 清理文本
    let text = bestContent
        .replace(/&nbsp;/g, ' ')
        .replace(/&amp;/g, '&')
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")
        .replace(/\s+/g, ' ')
        .trim();

    // 进一步清理垃圾内容
    text = cleanJunkContent(text);

    return {
        title: title,
        text: text,
        description: descMatch ? descMatch[1] : '',
        keywords: keywordsMatch ? keywordsMatch[1] : '',
        contentScore: bestScore
    };
}

/**
 * 计算文本质量分数
 * @param {string} text - 文本内容
 * @returns {number} - 质量分数
 */
function calculateTextQuality(text) {
    if (!text || text.length < 50) return 0;

    let score = text.length;

    // JavaScript代码惩罚
    const jsMatches = (text.match(/function\s*\(|var\s+\w+\s*=|const\s+\w+\s*=|let\s+\w+\s*=/g) || []).length;
    if (jsMatches > 5) score *= 0.3;

    // CSS代码惩罚
    const cssMatches = (text.match(/\w+\s*:\s*[^;]+;|\.\w+\s*{/g) || []).length;
    if (cssMatches > 3) score *= 0.3;

    // 重复字符惩罚
    const repeatedChars = text.match(/(.)\1{10,}/g);
    if (repeatedChars) score *= 0.5;

    // 句子结构加分
    const sentences = text.split(/[.!?。！？]/).filter(s => s.trim().length > 10);
    score += sentences.length * 10;

    return score;
}

/**
 * 清理垃圾内容
 * @param {string} text - 原始文本
 * @returns {string} - 清理后的文本
 */
function cleanJunkContent(text) {
    return text
        // 移除JavaScript代码片段
        .replace(/function\s*\([^)]*\)\s*\{[^}]*\}/g, '')
        .replace(/\w+\.\w+\([^)]*\)/g, '')
        .replace(/var\s+\w+\s*=\s*[^;]+;/g, '')
        .replace(/const\s+\w+\s*=\s*[^;]+;/g, '')
        .replace(/let\s+\w+\s*=\s*[^;]+;/g, '')
        // 移除CSS样式
        .replace(/\w+\s*:\s*[^;]+;/g, '')
        .replace(/\.\w+\s*\{[^}]*\}/g, '')
        // 移除重复的标点符号
        .replace(/[.]{3,}/g, '...')
        .replace(/[-]{3,}/g, '---')
        .replace(/[=]{3,}/g, '===')
        // 移除过长的重复字符
        .replace(/(.)\1{5,}/g, '$1$1$1')
        // 移除数字序列（可能是代码）
        .replace(/\b\d{10,}\b/g, '')
        // 清理行尾空格和多余换行
        .split('\n')
        .map(line => line.trim())
        .filter(line => line.length > 0 && !isJunkLine(line))
        .join('\n')
        .trim();
}

/**
 * 判断是否为垃圾行
 * @param {string} line - 文本行
 * @returns {boolean} - 是否为垃圾行
 */
function isJunkLine(line) {
    // 过短的行
    if (line.length < 10) return true;

    // 主要是数字和符号的行
    if (/^[\d\s\-_=+*/.,:;!@#$%^&()[\]{}|\\]+$/.test(line)) return true;

    // JavaScript代码行
    if (/^(function|var|const|let|if|for|while|return)\s/.test(line)) return true;

    // CSS代码行
    if (/^\s*[\w-]+\s*:\s*[^;]+;?\s*$/.test(line)) return true;

    // 重复字符行
    if (/(.)\1{8,}/.test(line)) return true;

    return false;
}

/**
 * 生成Markdown显示内容
 * @param {Object} result - 解析结果
 * @returns {string} - Markdown格式的显示内容
 */
function generateMarkdownDisplay(result) {
    let markdown = `# ${result.title}\n\n`;

    markdown += `**URL:** ${result.url}\n\n`;
    markdown += `**获取方法:** ${result.method}\n\n`;
    markdown += `**内容长度:** ${result.text.length} 字符\n\n`;

    // 添加token信息
    if (result.tokenInfo) {
        if (result.tokenInfo.originalTokens !== undefined) {
            markdown += `**Token信息:**\n`;
            markdown += `- 原始tokens: ${result.tokenInfo.originalTokens}\n`;
            markdown += `- 最终tokens: ${result.tokenInfo.finalTokens}\n`;
            if (result.tokenInfo.truncated) {
                markdown += `- 状态: 已截断\n`;
                markdown += `- 最大限制: ${result.tokenInfo.maxTokens} tokens\n`;
            } else {
                markdown += `- 状态: 未截断\n`;
            }
            markdown += '\n';
        } else if (result.tokenInfo.originalLength !== undefined) {
            markdown += `**长度信息:**\n`;
            markdown += `- 原始长度: ${result.tokenInfo.originalLength} 字符\n`;
            markdown += `- 最终长度: ${result.tokenInfo.finalLength} 字符\n`;
            if (result.tokenInfo.truncated) {
                markdown += `- 状态: 已截断\n`;
                markdown += `- 最大限制: ${result.tokenInfo.maxLength} 字符\n`;
            } else {
                markdown += `- 状态: 未截断\n`;
            }
            markdown += '\n';
        }
    }

    // 添加元数据信息
    if (result.metadata) {
        if (result.metadata.description) {
            markdown += `**描述:** ${result.metadata.description}\n\n`;
        }
        if (result.metadata.contentScore) {
            markdown += `**内容质量分数:** ${Math.round(result.metadata.contentScore)}\n\n`;
        }
        if (result.metadata.extractionMethod) {
            markdown += `**提取方法:** ${result.metadata.extractionMethod}\n\n`;
        }
    }

    markdown += `**解析时间:** ${new Date(result.timestamp).toLocaleString('zh-CN')}\n\n`;
    markdown += `---\n\n`;
    markdown += `**网页内容:**\n\n${result.text}`;

    return markdown;
}

/**
 * 检查内容质量
 * @param {Object} result - 解析结果
 * @returns {boolean} - 是否为高质量内容
 */
function checkContentQuality(result) {
    if (!result || !result.text) return false;

    const textLength = result.text.length;
    const contentScore = result.metadata?.contentScore || 0;
    const extractionMethod = result.metadata?.extractionMethod || '';

    // 质量检查标准
    const minLength = 100; // 最小内容长度
    const minScore = 50;   // 最小质量分数

    // 检查是否使用了fallback方法
    const isFallback = extractionMethod.includes('fallback') ||
                      extractionMethod.includes('error') ||
                      extractionMethod === 'BODY';

    // 检查内容是否主要是标题
    const titleLength = result.title?.length || 0;
    const isMainlyTitle = textLength > 0 && textLength <= titleLength + 10;

    logger.debug('网页解析工具', `内容质量检查: 长度=${textLength}, 分数=${contentScore}, 方法=${extractionMethod}, 是否fallback=${isFallback}, 主要是标题=${isMainlyTitle}`);

    return textLength >= minLength &&
           contentScore >= minScore &&
           !isFallback &&
           !isMainlyTitle;
}

/**
 * 比较两个解析结果，返回更好的那个
 * @param {Object} puppeteerResult - Puppeteer结果
 * @param {Object} axiosResult - Axios结果
 * @returns {Object} - 更好的结果
 */
function compareResults(puppeteerResult, axiosResult) {
    const puppeteerScore = calculateResultScore(puppeteerResult);
    const axiosScore = calculateResultScore(axiosResult);

    logger.debug('网页解析工具', `结果比较: Puppeteer分数=${puppeteerScore}, Axios分数=${axiosScore}`);

    if (puppeteerScore >= axiosScore) {
        return puppeteerResult;
    } else {
        return axiosResult;
    }
}

/**
 * 计算结果综合分数
 * @param {Object} result - 解析结果
 * @returns {number} - 综合分数
 */
function calculateResultScore(result) {
    if (!result || !result.text) return 0;

    let score = 0;

    // 内容长度分数 (0-40分)
    const textLength = result.text.length;
    score += Math.min(40, textLength / 100);

    // 质量分数 (0-30分)
    const contentScore = result.metadata?.contentScore || 0;
    score += Math.min(30, contentScore / 100);

    // 提取方法分数 (0-30分)
    const extractionMethod = result.metadata?.extractionMethod || '';
    if (extractionMethod.includes('fallback') || extractionMethod.includes('error')) {
        score += 0; // fallback方法得0分
    } else if (extractionMethod === 'BODY') {
        score += 10; // body方法得10分
    } else {
        score += 30; // 专用选择器得满分
    }

    return score;
}

/**
 * 主要的网页内容获取函数
 * @param {string} url - 网页URL
 * @returns {Promise<Object>} - 网页内容对象
 */
async function fetchAndCleanContent(url) {
    // 处理URL
    const processedUrl = processUrl(url);
    if (!processedUrl) {
        throw new Error('无效的URL格式');
    }

    logger.info('网页解析工具', `开始智能解析网页: ${processedUrl}`);

    let puppeteerResult = null;
    let puppeteerError = null;

    // 首先尝试使用Puppeteer
    if (puppeteer) {
        try {
            logger.debug('网页解析工具', '尝试使用Puppeteer获取内容');
            puppeteerResult = await fetchWithPuppeteer(processedUrl);

            const isGoodQuality = checkContentQuality(puppeteerResult);
            logger.info('网页解析工具', `Puppeteer获取完成，内容长度: ${puppeteerResult.text.length}, 质量: ${isGoodQuality ? '良好' : '不佳'}`);

            if (isGoodQuality) {
                logger.info('网页解析工具', 'Puppeteer内容质量良好，直接返回');
                return puppeteerResult;
            } else {
                logger.warning('网页解析工具', 'Puppeteer内容质量不佳，尝试axios备用方案');
            }
        } catch (error) {
            puppeteerError = error;
            logger.warning('网页解析工具', `Puppeteer获取失败: ${error.message}`);
        }
    } else {
        logger.warning('网页解析工具', 'Puppeteer不可用，直接使用axios');
    }

    // 尝试使用axios作为备用方案
    try {
        logger.info('网页解析工具', '尝试使用axios备用方案');
        const axiosResult = await fetchWithAxios(processedUrl);
        logger.info('网页解析工具', `axios获取完成，内容长度: ${axiosResult.text.length}`);

        // 如果有Puppeteer结果，比较两者质量
        if (puppeteerResult) {
            const betterResult = compareResults(puppeteerResult, axiosResult);
            logger.info('网页解析工具', `选择更好的结果: ${betterResult.method}`);
            return betterResult;
        } else {
            logger.info('网页解析工具', 'axios备用方案获取成功');
            return axiosResult;
        }
    } catch (axiosError) {
        logger.error('网页解析工具', `axios备用方案也失败: ${axiosError.message}`);

        // 如果有Puppeteer结果（即使质量不佳），也返回它
        if (puppeteerResult) {
            logger.warning('网页解析工具', '返回质量不佳的Puppeteer结果作为最后选择');
            return puppeteerResult;
        }

        throw new Error(`所有获取方法都失败了。Puppeteer: ${puppeteerError?.message || '不可用'}; Axios: ${axiosError.message}`);
    }
}

/**
 * 处理stdin输入的主函数
 */
function handleStdioInput() {
    let inputData = '';

    process.stdin.on('data', (chunk) => {
        inputData += chunk.toString();
    });

    process.stdin.on('end', async () => {
        let output;

        try {
            //logger.info('网页解析工具', '开始处理输入数据');

            // 解析输入的JSON
            let parsedInput;
            try {
                parsedInput = JSON.parse(inputData.trim());
            } catch (parseError) {
                throw new Error(`输入JSON解析失败: ${parseError.message}`);
            }

            // 验证必需参数
            if (!parsedInput.url) {
                throw new Error('缺少必需参数: url');
            }

            // 获取网页内容
            const result = await fetchAndCleanContent(parsedInput.url);

            logger.info('网页解析工具', `网页内容获取成功，标题: ${result.title}, 内容长度: ${result.text.length}`);

            // 构建返回结果
            const finalResult = {
                status: 'success',
                message: '网页内容解析完成',
                data: {
                    url: result.url,
                    title: result.title,
                    content: result.text,
                    content_length: result.text.length,
                    metadata: result.metadata,
                    method: result.method,
                    timestamp: result.timestamp,
                    token_info: result.tokenInfo,
                    markdown_display: generateMarkdownDisplay(result)
                }
            };

            // 确保输出是JSON格式
            output = JSON.stringify(finalResult);

        } catch (error) {
            let errorMessage;
            if (error.code === 'ENOTFOUND') {
                errorMessage = "网络连接失败，请检查网络设置。";
            } else if (error.code === 'ECONNREFUSED') {
                errorMessage = "连接被拒绝，请检查URL是否正确。";
            } else if (error.message.includes('timeout')) {
                errorMessage = "请求超时，请稍后重试。";
            } else if (error.message.includes('无效的URL')) {
                errorMessage = "URL格式无效，请提供有效的网页链接。";
            } else {
                errorMessage = error.message || "发生未知错误。";
            }

            logger.error('网页解析工具', `处理失败: ${errorMessage}`);

            // 返回标准错误JSON格式
            output = JSON.stringify({
                status: 'error',
                message: `网页解析失败: ${errorMessage}`,
                data: {
                    url: parsedInput?.url || '',
                    error_type: error.name || 'UnknownError',
                    error_details: errorMessage,
                    timestamp: new Date().toISOString()
                }
            });
        }

        // 输出JSON到stdout
        process.stdout.write(output);
    });
}

// 主程序入口
if (require.main === module) {
    // 检查是否有stdin输入
    if (!process.stdin.isTTY) {
        // 有stdin输入，作为同步插件处理JSON输入
        handleStdioInput();
    } else {
        // 无stdin输入，输出错误信息
        console.error('WebParserTool插件需要通过stdin接收JSON输入');
        process.exit(1);
    }
}
