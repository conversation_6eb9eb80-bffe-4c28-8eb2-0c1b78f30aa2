const pptxgen = require("pptxgenjs");
const { htmlToPptxText } = require("html2pptxgenjs");
const fs = require("fs");
const path = require("path");

// 使用 Node 18+ 原生 fetch
const { fetch } = globalThis;

// 下载远程图片保存为本地临时文件
async function downloadImage(url, filename) {
  const res = await fetch(url);
  const buffer = Buffer.from(await res.arrayBuffer());
  const tempPath = path.join(__dirname, filename);
  fs.writeFileSync(tempPath, buffer);
  return tempPath;
}

// 随机图像API
const getRandomImage = async (name) => {
  const url = "https://imgapi.cn/loading.php?return=img";
  return await downloadImage(url, `${name}.jpg`);
};

async function createFullPpt() {
  const pres = new pptxgen();
  pres.layout = "LAYOUT_16x9";

  // --- 幻灯片 1：欢迎页（背景图 + 标题） ---
  const bgImage = await getRandomImage("welcome-bg");
  const slide1 = pres.addSlide();
  slide1.background = { path: bgImage };

  slide1.addText("欢迎使用 AI 办公助手", {
    x: 0.5, y: 1.5, w: "90%", fontSize: 48,
    bold: true, color: "FFFFFF", align: "center",
    shadow: { type: "outer", angle: 45, blur: 5, offset: 2, color: "000000" }
  });

  slide1.addText("高效 · 智能 · 全面 · 一站式解决方案", {
    x: 0.5, y: 3.2, w: "90%", fontSize: 24,
    italic: true, color: "EEEEEE", align: "center"
  });

  // --- 幻灯片 2：产品功能页（图文左右排） ---
  const slide2 = pres.addSlide();

  const featureImage = await getRandomImage("features");
  slide2.addImage({ path: featureImage, x: 5.3, y: 1.4, w: 4, h: 3 });

  const featureHtml = `
    <div>
      <h2 style="color:#003366;">核心功能</h2>
      <ul>
        <li><b>AI 写作助手：</b> 一键生成文案</li>
        <li><b>语音识别：</b> 实时转写，自动校对</li>
        <li><b>智能摘要：</b> 摘要生成、要点提炼</li>
        <li><b>多语言翻译：</b> 覆盖超 100 种语言</li>
      </ul>
    </div>
  `;
  const featureItems = htmlToPptxText(featureHtml);
  slide2.addText(featureItems, { x: 0.7, y: 1.4, w: 4.5, h: 3.8, fontSize: 18 });

  // --- 幻灯片 3：使用场景页（卡片式图文） ---
  const slide3 = pres.addSlide();

  slide3.addText("应用场景", {
    x: 0.5, y: 0.5, w: "90%", fontSize: 36,
    bold: true, color: "333366", align: "center"
  });

  const scenes = [
    { title: "企业汇报", text: "快速生成报告大纲与演示文稿" },
    { title: "客户支持", text: "智能客服与自动知识回复" },
    { title: "教育培训", text: "课程内容制作与字幕自动生成" }
  ];

  for (let i = 0; i < scenes.length; i++) {
    const img = await getRandomImage(`scene-${i}`);
    const y = 1.6 + i * 1.8;

    slide3.addImage({ path: img, x: 0.7, y, w: 2.2, h: 1.4 });
    slide3.addText(scenes[i].title, { x: 3.1, y, fontSize: 20, bold: true, color: "003366" });
    slide3.addText(scenes[i].text, { x: 3.1, y: y + 0.6, fontSize: 16, color: "444444" });
  }

  // --- 幻灯片 4：客户案例页（图+介绍） ---
  const slide4 = pres.addSlide();

  const customerImage = await getRandomImage("customer");
  slide4.addText("客户成功案例", {
    x: 0.5, y: 0.6, w: "90%", fontSize: 34,
    bold: true, color: "222244", align: "center"
  });

  slide4.addImage({ path: customerImage, x: 1, y: 1.5, w: 4.5, h: 3 });

  const customerHtml = `
    <div>
      <h3>【科创云】</h3>
      <p>借助 AI 办公助手，完成了 10,000+ 客户资料的智能录入与自动归类。</p>
      <ul>
        <li>文档智能命名</li>
        <li>分类标签自动生成</li>
        <li>生成培训材料</li>
      </ul>
    </div>
  `;
  const customerItems = htmlToPptxText(customerHtml);
  slide4.addText(customerItems, { x: 5.8, y: 1.5, w: 3, h: 3.5, fontSize: 16 });

  // --- 幻灯片 5：联系我们 ---
  const slide5 = pres.addSlide();
  const contactBg = await getRandomImage("footer");
  slide5.background = { path: contactBg };

  const contactHtml = `
    <div>
      <h2 style="color:#FFFFFF;">联系我们</h2>
      <p style="color:#EEEEEE;"><b>官网：</b> www.smartai.com</p>
      <p style="color:#EEEEEE;"><b>电话：</b> 400-800-1234</p>
      <p style="color:#EEEEEE;"><b>邮箱：</b> <EMAIL></p>
    </div>
  `;
  const contactItems = htmlToPptxText(contactHtml);
  slide5.addText(contactItems, {
    x: 1.5, y: 2.5, w: 7,
    h: 2, fontSize: 20,
    color: "FFFFFF", align: "center"
  });

  // --- 保存文件 ---
  await pres.writeFile("ai-office-presentation.pptx");
  console.log("✅ 演示文稿创建成功！");
}

createFullPpt();
